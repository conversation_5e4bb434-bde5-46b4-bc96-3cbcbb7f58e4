import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:design_system/design/theme.dart';

class WeeklyCalendar extends StatefulWidget {
  final DateTime selectedDate;
  final void Function(DateTime) onDateSelected;
  final Set<DateTime> periodDates;
  final Set<DateTime> ovulationDates;

  const WeeklyCalendar({
    required this.selectedDate,
    required this.onDateSelected,
    this.periodDates = const {},
    this.ovulationDates = const {},
    Key? key,
  }) : super(key: key);

  @override
  State<WeeklyCalendar> createState() => _WeeklyCalendarState();
}

class _WeeklyCalendarState extends State<WeeklyCalendar> {
  late DateTime _currentWeek;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _currentWeek = _getWeekStart(widget.selectedDate);
    _pageController = PageController(
        initialPage: 1000); // Start in middle for infinite scroll
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  DateTime _getWeekStart(DateTime date) {
    // Get the start of the week (Sunday)
    final weekday = date.weekday % 7; // Sunday = 0
    return DateTime(date.year, date.month, date.day - weekday);
  }

  List<DateTime> _getWeekDates(DateTime weekStart) {
    return List.generate(7, (index) => weekStart.add(Duration(days: index)));
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  bool _isPeriodDate(DateTime date) {
    return widget.periodDates.any((periodDate) => _isSameDay(date, periodDate));
  }

  bool _isOvulationDate(DateTime date) {
    return widget.ovulationDates
        .any((ovulationDate) => _isSameDay(date, ovulationDate));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(32),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Month/Year header with navigation
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: () {
                  setState(() {
                    _currentWeek = _currentWeek.subtract(Duration(days: 7));
                  });
                },
                icon: Icon(Icons.chevron_left, color: AppTheme.primaryColor),
              ),
              Text(
                DateFormat('MMMM yyyy').format(_currentWeek),
                style: GoogleFonts.mulish(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Color(0xff2D2D2D),
                ),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    _currentWeek = _currentWeek.add(Duration(days: 7));
                  });
                },
                icon: Icon(Icons.chevron_right, color: AppTheme.primaryColor),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Days of week header
          Row(
            children: ['S', 'M', 'T', 'W', 'T', 'F', 'S']
                .map((day) => Expanded(
                      child: Center(
                        child: Text(
                          day,
                          style: GoogleFonts.mulish(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            color: Color(0xff666666),
                          ),
                        ),
                      ),
                    ))
                .toList(),
          ),

          SizedBox(height: 12.h),

          // Week dates
          Row(
            children: _getWeekDates(_currentWeek).map((date) {
              final isSelected = _isSameDay(date, widget.selectedDate);
              final isPeriod = _isPeriodDate(date);
              final isOvulation = _isOvulationDate(date);
              final isToday = _isSameDay(date, DateTime.now());
              final isFuture = date.isAfter(DateTime.now());

              return Expanded(
                child: GestureDetector(
                  onTap: () => widget.onDateSelected(date),
                  child: Container(
                    height: 48.h,
                    margin: EdgeInsets.symmetric(horizontal: 2.w),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected
                          ? AppTheme.primaryColor
                          : isPeriod
                              ? AppTheme.primaryColor.withValues(alpha: 0.2)
                              : isOvulation
                                  ? Color(0xffECA83D).withValues(alpha: 0.2)
                                  : Colors.transparent,
                      border: isToday && !isSelected
                          ? Border.all(color: AppTheme.primaryColor, width: 2)
                          : null,
                    ),
                    child: Center(
                      child: Text(
                        '${date.day}',
                        style: GoogleFonts.mulish(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500,
                          color: isSelected
                              ? Colors.white
                              : isFuture
                                  ? Color(0xffCCCCCC)
                                  : isPeriod || isOvulation
                                      ? AppTheme.primaryColor
                                      : Color(0xff2D2D2D),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}

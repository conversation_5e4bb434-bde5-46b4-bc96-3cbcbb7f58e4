import 'package:account_management/application/symptom_tracking_bloc/symptom_tracking_bloc.dart';
import 'package:account_management/domain/model/symptom_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import '../../custom_widgets/emoji_slider.dart';

class DailySymptomPage extends StatefulWidget {
  final String? scrollToSection; // 'pain', 'symptoms', or 'flow'

  const DailySymptomPage({
    this.scrollToSection,
    Key? key,
  }) : super(key: key);

  @override
  State<DailySymptomPage> createState() => _DailySymptomPageState();
}

class _DailySymptomPageState extends State<DailySymptomPage> {
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _painSectionKey = GlobalKey();
  final GlobalKey _symptomsSectionKey = GlobalKey();
  final GlobalKey _flowSectionKey = GlobalKey();

  // Symptom tracking state
  List<SymptomModel> _selectedSymptoms = [];
  int _painLevel = 0;
  int _flowLevel = 0;

  List<String> emoji = [
    '\u{1F601}', // 😁
    '\u{1F642}', // 🙂
    '\u{1F610}', // 😐
    '\u{1F615}', // 😕
    '\u{1F641}', // 🙁
    '\u{1F61E}', // 😞
    '\u{1F613}', // 😓
    '\u{1F623}', // 😣
    '\u{1F616}', // 😖
    '\u{1F635}\u{200D}\u{1F4AB}', // 😵‍💫
    '\u{1F635}\u{200D}\u{1F4AB}', // 😵‍💫
  ];

  List<String> painDescriptions = [
    'No Pain',
    'Discomfort',
    'Very Mild',
    'Mild',
    'Moderate',
    'Significant',
    'High',
    'Very High',
    'Intense',
    'Worst Pain',
    'Worst Pain',
  ];

  List<SymptomModel> _symptoms = [
    SymptomModel(name: 'Headache'),
    SymptomModel(name: 'Fatigue'),
    SymptomModel(name: 'Bloating'),
    SymptomModel(name: 'Back Pain'),
    SymptomModel(name: 'Cramps'),
    SymptomModel(name: 'Breakouts'),
  ];

  @override
  void initState() {
    super.initState();

    // Scroll to specific section if requested
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.scrollToSection != null) {
        _scrollToSection(widget.scrollToSection!);
      }
    });
  }

  void _scrollToSection(String section) {
    GlobalKey? targetKey;
    switch (section) {
      case 'pain':
        targetKey = _painSectionKey;
        break;
      case 'symptoms':
        targetKey = _symptomsSectionKey;
        break;
      case 'flow':
        targetKey = _flowSectionKey;
        break;
    }

    if (targetKey?.currentContext != null) {
      Scrollable.ensureVisible(
        targetKey!.currentContext!,
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  bool _canEditSymptoms(DateTime date) {
    final today = DateTime.now();
    final dateOnly = DateTime(date.year, date.month, date.day);
    final todayOnly = DateTime(today.year, today.month, today.day);
    return dateOnly.isBefore(todayOnly) || dateOnly.isAtSameMomentAs(todayOnly);
  }

  bool _canGoToNextDay(DateTime selectedDate) {
    final today = DateTime.now();
    final nextDay = selectedDate.add(Duration(days: 1));
    final nextDayOnly = DateTime(nextDay.year, nextDay.month, nextDay.day);
    final todayOnly = DateTime(today.year, today.month, today.day);
    return nextDayOnly.isBefore(todayOnly) ||
        nextDayOnly.isAtSameMomentAs(todayOnly);
  }

  void _changeDate(int days, DateTime currentDate) {
    final newDate = currentDate.add(Duration(days: days));

    // Use the bloc to change the date
    context.read<SymptomTrackingBloc>().add(
          SymptomTrackingEvent.dateChanged(newDate: newDate),
        );
  }

  void _saveSymptoms(DateTime selectedDate) {
    context.read<SymptomTrackingBloc>().add(
          SymptomTrackingEvent.saveSymptomData(
            date: selectedDate,
            symptoms: _selectedSymptoms.isNotEmpty ? _selectedSymptoms : null,
            painLevel: _painLevel > 0 ? _painLevel : null,
            flowLevel: _flowLevel > 0 ? _flowLevel : null,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SymptomTrackingBloc, SymptomTrackingState>(
      listener: (context, state) {
        state.when(
          initial: (selectedDate) {},
          loading: (selectedDate) {},
          loaded: (selectedDate, symptoms, painLevel, flowLevel) {
            // Update UI state with loaded data
            setState(() {
              _selectedSymptoms = symptoms ?? [];
              _painLevel = painLevel ?? 0;
              _flowLevel = flowLevel ?? 0;
            });
          },
          success: (selectedDate) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Symptoms saved successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.of(context).pop();
          },
          failure: (selectedDate, failure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to save symptoms'),
                backgroundColor: Colors.red,
              ),
            );
          },
        );
      },
      child: BlocBuilder<SymptomTrackingBloc, SymptomTrackingState>(
        builder: (context, state) {
          final selectedDate = state.when(
            initial: (date) => date ?? DateTime.now(),
            loading: (date) => date ?? DateTime.now(),
            loaded: (date, _, __, ___) => date,
            success: (date) => date,
            failure: (date, _) => date ?? DateTime.now(),
          );

          return Scaffold(
            backgroundColor: Colors.transparent,
            bottomNavigationBar: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(32),
                  bottomRight: Radius.circular(32),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.all(20.w),
                child: SizedBox(
                  width: double.infinity,
                  height: 50.h,
                  child: ElevatedButton(
                    onPressed: _canEditSymptoms(selectedDate)
                        ? () => _saveSymptoms(selectedDate)
                        : null,
                    style: ButtonStyle(
                      backgroundColor: WidgetStatePropertyAll(
                        _canEditSymptoms(selectedDate)
                            ? Color.fromRGBO(58, 38, 101, 1.0)
                            : Colors.grey,
                      ),
                      shape: WidgetStatePropertyAll(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                      ),
                    ),
                    child:
                        BlocBuilder<SymptomTrackingBloc, SymptomTrackingState>(
                      builder: (context, state) {
                        return state.when(
                          initial: (date) => Text(
                            _canEditSymptoms(selectedDate)
                                ? 'Save Symptoms'
                                : 'Cannot edit future dates',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          loading: (date) => SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          loaded: (date, _, __, ___) => Text(
                            _canEditSymptoms(selectedDate)
                                ? 'Save Symptoms'
                                : 'Cannot edit future dates',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          success: (date) => Text(
                            'Save Symptoms',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          failure: (date, _) => Text(
                            'Save Symptoms',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
            body: Container(
              height: MediaQuery.of(context).size.height * 0.9,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(32),
                  topRight: Radius.circular(32),
                ),
              ),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    margin: EdgeInsets.only(top: 12.h),
                    width: 40.w,
                    height: 4.h,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // Header with date navigation
                  Padding(
                    padding: EdgeInsets.all(24.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Previous day button
                        IconButton(
                          onPressed: () => _changeDate(-1, selectedDate),
                          icon: Icon(
                            Icons.arrow_back_ios,
                            color: Color.fromRGBO(58, 38, 101, 1.0),
                            size: 20,
                          ),
                        ),
                        // Date display
                        Expanded(
                          child: Center(
                            child: Text(
                              DateFormat('EEEE, MMMM d, y')
                                  .format(selectedDate),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                        // Next day button
                        IconButton(
                          onPressed: _canGoToNextDay(selectedDate)
                              ? () => _changeDate(1, selectedDate)
                              : null,
                          icon: Icon(
                            Icons.arrow_forward_ios,
                            color: _canGoToNextDay(selectedDate)
                                ? Color.fromRGBO(58, 38, 101, 1.0)
                                : Colors.grey,
                            size: 20,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Content
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(15.0),
                      child: ListView(
                        controller: _scrollController,
                        shrinkWrap: true,
                        children: [
                          SizedBox(height: 20),
                          // Pain Level Section - Exact copy from original
                          Container(
                            key: _painSectionKey,
                            margin: EdgeInsets.zero,
                            height: 0.6.sw,
                            width: 0.90.sw,
                            decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                  color: Color(0x40000000),
                                  blurRadius: 4.0,
                                  offset: Offset(0, 1),
                                ),
                              ],
                              borderRadius: BorderRadius.circular(32),
                              color: Color.fromRGBO(250, 242, 223, 1),
                            ),
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 30.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SizedBox(height: 10),
                                  Text(
                                    "What is Your Overall Pain?",
                                    style: TextStyle(
                                      color: Color.fromRGBO(58, 38, 101, 1.0),
                                      fontWeight: FontWeight.w700,
                                      fontSize: 22,
                                    ),
                                  ),
                                  SizedBox(height: 30),
                                  Text(
                                    _painLevel.toString(),
                                    style: TextStyle(
                                      color: Color.fromRGBO(58, 38, 101, 1.0),
                                      fontWeight: FontWeight.w700,
                                      fontSize: 31,
                                    ),
                                  ),
                                  SizedBox(height: 10),
                                  Container(
                                    height: 77,
                                    margin: EdgeInsets.zero,
                                    child: Align(
                                      child: EmojiSlider(
                                        key: Key('emoji_slider'),
                                        currentValue: _painLevel.toDouble(),
                                        emojis: emoji,
                                        minValue: 0,
                                        maxValue: 10,
                                        labels: painDescriptions,
                                        onChanged:
                                            _canEditSymptoms(selectedDate)
                                                ? (handlerIndex, lowerValue,
                                                    upperValue) {
                                                    setState(() {
                                                      _painLevel =
                                                          (lowerValue as double)
                                                              .toInt();
                                                    });
                                                  }
                                                : null,
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 5.0),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'No Pain',
                                          style: TextStyle(color: Colors.black),
                                        ),
                                        Text(
                                          'Worst Pain',
                                          style: TextStyle(color: Colors.black),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: 20),
                          // Symptoms Section - Exact copy from original
                          Container(
                            key: _symptomsSectionKey,
                            decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                  color: Color(0x40000000),
                                  blurRadius: 4.0,
                                  offset: Offset(0, 1),
                                ),
                              ],
                              borderRadius: BorderRadius.circular(32),
                              color: Color.fromRGBO(250, 242, 223, 1),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  "Symptoms",
                                  style: TextStyle(
                                    color: Color.fromRGBO(58, 38, 101, 1.0),
                                    fontWeight: FontWeight.w700,
                                    fontSize: 22,
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(15.0),
                                  child: GridView.builder(
                                    padding: EdgeInsets.zero,
                                    shrinkWrap: true,
                                    physics: NeverScrollableScrollPhysics(),
                                    gridDelegate:
                                        SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 3,
                                      crossAxisSpacing: 10.0,
                                      mainAxisSpacing: 0.0,
                                      mainAxisExtent: 80.0,
                                    ),
                                    itemCount: _symptoms.length,
                                    itemBuilder: (gridContext, index) {
                                      final isSelected = _selectedSymptoms.any(
                                          (symptom) =>
                                              symptom.name ==
                                              _symptoms[index].name);
                                      return GestureDetector(
                                        onTap: _canEditSymptoms(selectedDate)
                                            ? () {
                                                setState(() {
                                                  if (isSelected) {
                                                    _selectedSymptoms
                                                        .removeWhere(
                                                            (symptom) =>
                                                                symptom.name ==
                                                                _symptoms[index]
                                                                    .name);
                                                  } else {
                                                    _selectedSymptoms
                                                        .add(_symptoms[index]);
                                                  }
                                                });
                                              }
                                            : null,
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Container(
                                              height: 38,
                                              width: 67,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(32),
                                                color: isSelected
                                                    ? Color.fromRGBO(
                                                        247, 166, 0, 1)
                                                    : Colors.white,
                                              ),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(4.0),
                                                child: SvgPicture.asset(
                                                  'assets/home/<USER>' ', '_')}.svg',
                                                  colorFilter: ColorFilter.mode(
                                                    isSelected
                                                        ? Colors.white
                                                        : Color.fromRGBO(
                                                            88, 66, 148, 1),
                                                    BlendMode.srcIn,
                                                  ),
                                                  fit: BoxFit.fitHeight,
                                                  height: 20,
                                                  width: 20,
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 4),
                                            Text(
                                              _symptoms[index].name,
                                              style: TextStyle(
                                                color: Color.fromRGBO(
                                                    58, 38, 101, 1.0),
                                                fontWeight: FontWeight.w700,
                                                fontSize: 15,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 20),
                          // Flow Section - Exact copy from original
                          Container(
                            key: _flowSectionKey,
                            height: 120,
                            decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                  color: Color(0x40000000),
                                  blurRadius: 4.0,
                                  offset: Offset(0, 1),
                                ),
                              ],
                              borderRadius: BorderRadius.circular(32),
                              color: Color.fromRGBO(250, 242, 223, 1),
                            ),
                            child: Column(
                              children: [
                                SizedBox(height: 10),
                                Text(
                                  "Flow",
                                  style: TextStyle(
                                    color: Color.fromRGBO(58, 38, 101, 1.0),
                                    fontWeight: FontWeight.w700,
                                    fontSize: 22,
                                  ),
                                ),
                                SizedBox(height: 5),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    _buildFlowButton(1,
                                        'assets/home/<USER>', selectedDate),
                                    _buildFlowButton(2,
                                        'assets/home/<USER>', selectedDate),
                                    _buildFlowButton(3,
                                        'assets/home/<USER>', selectedDate),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 5),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFlowButton(int level, String assetPath, DateTime selectedDate) {
    final isSelected = _flowLevel == level;
    return Container(
      height: 45,
      width: 90,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        color: Color.fromRGBO(250, 242, 223, 1),
      ),
      child: TextButton(
        key: Key('flow_level_$level'),
        style: ButtonStyle(
          padding: WidgetStatePropertyAll(EdgeInsets.zero),
          backgroundColor: isSelected
              ? WidgetStatePropertyAll(Color.fromRGBO(247, 166, 0, 1))
              : WidgetStatePropertyAll(Color.fromRGBO(250, 242, 223, 1)),
          foregroundColor: isSelected
              ? WidgetStatePropertyAll(Colors.white)
              : WidgetStatePropertyAll(Colors.purple),
        ),
        onPressed: _canEditSymptoms(selectedDate)
            ? () {
                setState(() {
                  _flowLevel = level;
                });
              }
            : null,
        child: SvgPicture.asset(
          assetPath,
          colorFilter: ColorFilter.mode(
            isSelected ? Colors.white : Color.fromRGBO(88, 66, 148, 1),
            BlendMode.srcIn,
          ),
        ),
      ),
    );
  }
}

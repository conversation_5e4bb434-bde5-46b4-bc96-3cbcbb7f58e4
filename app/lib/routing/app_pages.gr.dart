// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i33;
import 'package:flutter/material.dart' as _i34;
import 'package:juno_plus/app.dart' as _i28;
import 'package:juno_plus/pages/connectivity/pair_page.dart' as _i16;
import 'package:juno_plus/pages/connectivity/troubleshooting_page.dart' as _i30;
import 'package:juno_plus/pages/dashboard/daily_symptom_tracking_page.dart'
    as _i1;
import 'package:juno_plus/pages/dashboard/dashboard_page.dart' as _i2;
import 'package:juno_plus/pages/dashboard/extended_calendar.dart' as _i5;
import 'package:juno_plus/pages/dashboard/new_dashboard_page.dart' as _i13;
import 'package:juno_plus/pages/dashboard/period_tracking_edit_page.dart'
    as _i17;
import 'package:juno_plus/pages/dashboard/period_tracking_view_page.dart'
    as _i18;
import 'package:juno_plus/pages/dashboard/therapy_analytics_chart_page.dart'
    as _i29;
import 'package:juno_plus/pages/help_center/help_center_home.dart' as _i7;
import 'package:juno_plus/pages/home_page.dart' as _i8;
import 'package:juno_plus/pages/login/email_verification_page.dart' as _i4;
import 'package:juno_plus/pages/login/login_page.dart' as _i9;
import 'package:juno_plus/pages/login/reset_password_page.dart' as _i25;
import 'package:juno_plus/pages/login/sign_up_page.dart' as _i27;
import 'package:juno_plus/pages/login/welcome_page.dart' as _i32;
import 'package:juno_plus/pages/medications/medication.dart' as _i12;
import 'package:juno_plus/pages/medications/medication_cabinet.dart' as _i11;
import 'package:juno_plus/pages/medications/medication_cabinet_button.dart'
    as _i10;
import 'package:juno_plus/pages/notifications/notifications_page.dart' as _i14;
import 'package:juno_plus/pages/onboarding/get_started_page.dart' as _i6;
import 'package:juno_plus/pages/onboarding/onboarding_start.dart' as _i15;
import 'package:juno_plus/pages/product_showcase/product_showcase.dart' as _i19;
import 'package:juno_plus/pages/remote/remote_experiment_Page.dart' as _i22;
import 'package:juno_plus/pages/remote/remote_one_page.dart' as _i23;
import 'package:juno_plus/pages/remote/remote_two_page.dart' as _i24;
import 'package:juno_plus/pages/settings/device_settings_page.dart' as _i3;
import 'package:juno_plus/pages/settings/profile_page.dart' as _i20;
import 'package:juno_plus/pages/settings/profile_picture_page.dart' as _i21;
import 'package:juno_plus/pages/settings/settings_page.dart' as _i26;
import 'package:juno_plus/pages/virtual_remote/virtual_remote_page.dart'
    as _i31;

/// generated route for
/// [_i1.DailySymptomTrackingPage]
class DailySymptomTrackingRoute
    extends _i33.PageRouteInfo<DailySymptomTrackingRouteArgs> {
  DailySymptomTrackingRoute({
    _i34.Key? key,
    DateTime? initialDate,
    List<_i33.PageRouteInfo>? children,
  }) : super(
          DailySymptomTrackingRoute.name,
          args: DailySymptomTrackingRouteArgs(
            key: key,
            initialDate: initialDate,
          ),
          initialChildren: children,
        );

  static const String name = 'DailySymptomTrackingRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<DailySymptomTrackingRouteArgs>(
        orElse: () => const DailySymptomTrackingRouteArgs(),
      );
      return _i1.DailySymptomTrackingPage(
        key: args.key,
        initialDate: args.initialDate,
      );
    },
  );
}

class DailySymptomTrackingRouteArgs {
  const DailySymptomTrackingRouteArgs({this.key, this.initialDate});

  final _i34.Key? key;

  final DateTime? initialDate;

  @override
  String toString() {
    return 'DailySymptomTrackingRouteArgs{key: $key, initialDate: $initialDate}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! DailySymptomTrackingRouteArgs) return false;
    return key == other.key && initialDate == other.initialDate;
  }

  @override
  int get hashCode => key.hashCode ^ initialDate.hashCode;
}

/// generated route for
/// [_i2.DashboardPage]
class DashboardRoute extends _i33.PageRouteInfo<void> {
  const DashboardRoute({List<_i33.PageRouteInfo>? children})
      : super(DashboardRoute.name, initialChildren: children);

  static const String name = 'DashboardRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return _i2.DashboardPage();
    },
  );
}

/// generated route for
/// [_i3.DeviceSettingsPage]
class DeviceSettingsRoute extends _i33.PageRouteInfo<void> {
  const DeviceSettingsRoute({List<_i33.PageRouteInfo>? children})
      : super(DeviceSettingsRoute.name, initialChildren: children);

  static const String name = 'DeviceSettingsRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i3.DeviceSettingsPage();
    },
  );
}

/// generated route for
/// [_i4.EmailVerificationPage]
class EmailVerificationRoute extends _i33.PageRouteInfo<void> {
  const EmailVerificationRoute({List<_i33.PageRouteInfo>? children})
      : super(EmailVerificationRoute.name, initialChildren: children);

  static const String name = 'EmailVerificationRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i4.EmailVerificationPage();
    },
  );
}

/// generated route for
/// [_i5.ExtendedCalenderPage]
class ExtendedCalenderRoute extends _i33.PageRouteInfo<void> {
  const ExtendedCalenderRoute({List<_i33.PageRouteInfo>? children})
      : super(ExtendedCalenderRoute.name, initialChildren: children);

  static const String name = 'ExtendedCalenderRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i5.ExtendedCalenderPage();
    },
  );
}

/// generated route for
/// [_i6.GetStartedPage]
class GetStartedRoute extends _i33.PageRouteInfo<void> {
  const GetStartedRoute({List<_i33.PageRouteInfo>? children})
      : super(GetStartedRoute.name, initialChildren: children);

  static const String name = 'GetStartedRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return _i6.GetStartedPage();
    },
  );
}

/// generated route for
/// [_i7.HelpCenterHomePage]
class HelpCenterHomeRoute extends _i33.PageRouteInfo<void> {
  const HelpCenterHomeRoute({List<_i33.PageRouteInfo>? children})
      : super(HelpCenterHomeRoute.name, initialChildren: children);

  static const String name = 'HelpCenterHomeRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i7.HelpCenterHomePage();
    },
  );
}

/// generated route for
/// [_i8.HomePage]
class HomeRoute extends _i33.PageRouteInfo<void> {
  const HomeRoute({List<_i33.PageRouteInfo>? children})
      : super(HomeRoute.name, initialChildren: children);

  static const String name = 'HomeRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i8.HomePage();
    },
  );
}

/// generated route for
/// [_i9.LoginPage]
class LoginRoute extends _i33.PageRouteInfo<void> {
  const LoginRoute({List<_i33.PageRouteInfo>? children})
      : super(LoginRoute.name, initialChildren: children);

  static const String name = 'LoginRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i9.LoginPage();
    },
  );
}

/// generated route for
/// [_i10.MedicationCabinetButton]
class MedicationCabinetButton extends _i33.PageRouteInfo<void> {
  const MedicationCabinetButton({List<_i33.PageRouteInfo>? children})
      : super(MedicationCabinetButton.name, initialChildren: children);

  static const String name = 'MedicationCabinetButton';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i10.MedicationCabinetButton();
    },
  );
}

/// generated route for
/// [_i11.MedicationCabinetPage]
class MedicationCabinetRoute extends _i33.PageRouteInfo<void> {
  const MedicationCabinetRoute({List<_i33.PageRouteInfo>? children})
      : super(MedicationCabinetRoute.name, initialChildren: children);

  static const String name = 'MedicationCabinetRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i11.MedicationCabinetPage();
    },
  );
}

/// generated route for
/// [_i12.MedicationPage]
class MedicationRoute extends _i33.PageRouteInfo<void> {
  const MedicationRoute({List<_i33.PageRouteInfo>? children})
      : super(MedicationRoute.name, initialChildren: children);

  static const String name = 'MedicationRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i12.MedicationPage();
    },
  );
}

/// generated route for
/// [_i13.NewDashboardPage]
class NewDashboardRoute extends _i33.PageRouteInfo<void> {
  const NewDashboardRoute({List<_i33.PageRouteInfo>? children})
      : super(NewDashboardRoute.name, initialChildren: children);

  static const String name = 'NewDashboardRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return _i13.NewDashboardPage();
    },
  );
}

/// generated route for
/// [_i14.NotificationsPage]
class NotificationsRoute extends _i33.PageRouteInfo<void> {
  const NotificationsRoute({List<_i33.PageRouteInfo>? children})
      : super(NotificationsRoute.name, initialChildren: children);

  static const String name = 'NotificationsRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i14.NotificationsPage();
    },
  );
}

/// generated route for
/// [_i15.OnboardingStartScreen]
class OnboardingStartScreen extends _i33.PageRouteInfo<void> {
  const OnboardingStartScreen({List<_i33.PageRouteInfo>? children})
      : super(OnboardingStartScreen.name, initialChildren: children);

  static const String name = 'OnboardingStartScreen';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i15.OnboardingStartScreen();
    },
  );
}

/// generated route for
/// [_i16.PairDevicePage]
class PairDeviceRoute extends _i33.PageRouteInfo<void> {
  const PairDeviceRoute({List<_i33.PageRouteInfo>? children})
      : super(PairDeviceRoute.name, initialChildren: children);

  static const String name = 'PairDeviceRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i16.PairDevicePage();
    },
  );
}

/// generated route for
/// [_i17.PeriodTrackingEditPage]
class PeriodTrackingEditRoute extends _i33.PageRouteInfo<void> {
  const PeriodTrackingEditRoute({List<_i33.PageRouteInfo>? children})
      : super(PeriodTrackingEditRoute.name, initialChildren: children);

  static const String name = 'PeriodTrackingEditRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i17.PeriodTrackingEditPage();
    },
  );
}

/// generated route for
/// [_i18.PeriodTrackingViewPage]
class PeriodTrackingViewRoute extends _i33.PageRouteInfo<void> {
  const PeriodTrackingViewRoute({List<_i33.PageRouteInfo>? children})
      : super(PeriodTrackingViewRoute.name, initialChildren: children);

  static const String name = 'PeriodTrackingViewRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i18.PeriodTrackingViewPage();
    },
  );
}

/// generated route for
/// [_i19.ProductShowcasePage]
class ProductShowcaseRoute extends _i33.PageRouteInfo<void> {
  const ProductShowcaseRoute({List<_i33.PageRouteInfo>? children})
      : super(ProductShowcaseRoute.name, initialChildren: children);

  static const String name = 'ProductShowcaseRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i19.ProductShowcasePage();
    },
  );
}

/// generated route for
/// [_i20.ProfilePage]
class ProfileRoute extends _i33.PageRouteInfo<ProfileRouteArgs> {
  ProfileRoute({_i34.Key? key, List<_i33.PageRouteInfo>? children})
      : super(
          ProfileRoute.name,
          args: ProfileRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ProfileRouteArgs>(
        orElse: () => const ProfileRouteArgs(),
      );
      return _i20.ProfilePage(key: args.key);
    },
  );
}

class ProfileRouteArgs {
  const ProfileRouteArgs({this.key});

  final _i34.Key? key;

  @override
  String toString() {
    return 'ProfileRouteArgs{key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ProfileRouteArgs) return false;
    return key == other.key;
  }

  @override
  int get hashCode => key.hashCode;
}

/// generated route for
/// [_i21.ProfilePicturePage]
class ProfilePictureRoute extends _i33.PageRouteInfo<void> {
  const ProfilePictureRoute({List<_i33.PageRouteInfo>? children})
      : super(ProfilePictureRoute.name, initialChildren: children);

  static const String name = 'ProfilePictureRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return _i21.ProfilePicturePage();
    },
  );
}

/// generated route for
/// [_i22.RemoteExperimentPage]
class RemoteExperimentRoute extends _i33.PageRouteInfo<void> {
  const RemoteExperimentRoute({List<_i33.PageRouteInfo>? children})
      : super(RemoteExperimentRoute.name, initialChildren: children);

  static const String name = 'RemoteExperimentRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return _i22.RemoteExperimentPage();
    },
  );
}

/// generated route for
/// [_i23.RemoteOnePage]
class RemoteOneRoute extends _i33.PageRouteInfo<void> {
  const RemoteOneRoute({List<_i33.PageRouteInfo>? children})
      : super(RemoteOneRoute.name, initialChildren: children);

  static const String name = 'RemoteOneRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return _i23.RemoteOnePage();
    },
  );
}

/// generated route for
/// [_i24.RemoteTwoPage]
class RemoteTwoRoute extends _i33.PageRouteInfo<void> {
  const RemoteTwoRoute({List<_i33.PageRouteInfo>? children})
      : super(RemoteTwoRoute.name, initialChildren: children);

  static const String name = 'RemoteTwoRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return _i24.RemoteTwoPage();
    },
  );
}

/// generated route for
/// [_i25.ResetPasswordPage]
class ResetPasswordRoute extends _i33.PageRouteInfo<void> {
  const ResetPasswordRoute({List<_i33.PageRouteInfo>? children})
      : super(ResetPasswordRoute.name, initialChildren: children);

  static const String name = 'ResetPasswordRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i25.ResetPasswordPage();
    },
  );
}

/// generated route for
/// [_i26.SettingsPage]
class SettingsRoute extends _i33.PageRouteInfo<void> {
  const SettingsRoute({List<_i33.PageRouteInfo>? children})
      : super(SettingsRoute.name, initialChildren: children);

  static const String name = 'SettingsRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i26.SettingsPage();
    },
  );
}

/// generated route for
/// [_i27.SignUpPage]
class SignUpRoute extends _i33.PageRouteInfo<void> {
  const SignUpRoute({List<_i33.PageRouteInfo>? children})
      : super(SignUpRoute.name, initialChildren: children);

  static const String name = 'SignUpRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i27.SignUpPage();
    },
  );
}

/// generated route for
/// [_i28.SplashPage]
class SplashRoute extends _i33.PageRouteInfo<void> {
  const SplashRoute({List<_i33.PageRouteInfo>? children})
      : super(SplashRoute.name, initialChildren: children);

  static const String name = 'SplashRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i28.SplashPage();
    },
  );
}

/// generated route for
/// [_i29.TherapyAnalyticsChartPage]
class TherapyAnalyticsChartRoute extends _i33.PageRouteInfo<void> {
  const TherapyAnalyticsChartRoute({List<_i33.PageRouteInfo>? children})
      : super(TherapyAnalyticsChartRoute.name, initialChildren: children);

  static const String name = 'TherapyAnalyticsChartRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return _i29.TherapyAnalyticsChartPage();
    },
  );
}

/// generated route for
/// [_i30.TroubleshootingPage]
class TroubleshootingRoute extends _i33.PageRouteInfo<void> {
  const TroubleshootingRoute({List<_i33.PageRouteInfo>? children})
      : super(TroubleshootingRoute.name, initialChildren: children);

  static const String name = 'TroubleshootingRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return _i30.TroubleshootingPage();
    },
  );
}

/// generated route for
/// [_i31.VirtualRemotePage]
class VirtualRemoteRoute extends _i33.PageRouteInfo<void> {
  const VirtualRemoteRoute({List<_i33.PageRouteInfo>? children})
      : super(VirtualRemoteRoute.name, initialChildren: children);

  static const String name = 'VirtualRemoteRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return _i31.VirtualRemotePage();
    },
  );
}

/// generated route for
/// [_i32.WelcomePage]
class WelcomeRoute extends _i33.PageRouteInfo<void> {
  const WelcomeRoute({List<_i33.PageRouteInfo>? children})
      : super(WelcomeRoute.name, initialChildren: children);

  static const String name = 'WelcomeRoute';

  static _i33.PageInfo page = _i33.PageInfo(
    name,
    builder: (data) {
      return const _i32.WelcomePage();
    },
  );
}
